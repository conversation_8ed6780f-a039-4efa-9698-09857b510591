/**
 * 边缘路由节点
 * 实现边缘路由、负载均衡、缓存、压缩、优化和服务质量管理功能
 */

import { BaseNode } from '../BaseNode';
import { NodeInput, NodeOutput } from '../../types/NodeTypes';

/**
 * 边缘路由节点
 * 提供智能边缘路由决策功能
 */
export class EdgeRoutingNode extends BaseNode {
  constructor() {
    super('EdgeRoutingNode', '边缘路由', '边缘计算');
    
    this.inputs = [
      { name: 'clientInfo', type: 'object', label: '客户端信息' },
      { name: 'routingPolicy', type: 'string', label: '路由策略' },
      { name: 'edgeNodes', type: 'array', label: '边缘节点列表' },
      { name: 'networkMetrics', type: 'object', label: '网络指标' }
    ];
    
    this.outputs = [
      { name: 'selectedNode', type: 'object', label: '选中节点' },
      { name: 'routingDecision', type: 'object', label: '路由决策' },
      { name: 'routingMetrics', type: 'object', label: '路由指标' }
    ];
  }

  async execute(inputs: Record<string, any>): Promise<Record<string, any>> {
    const { clientInfo, routingPolicy = 'latency', edgeNodes = [], networkMetrics = {} } = inputs;

    try {
      // 路由决策算法
      const routingDecision = await this.makeRoutingDecision(
        clientInfo,
        routingPolicy,
        edgeNodes,
        networkMetrics
      );

      return {
        selectedNode: routingDecision.selectedNode,
        routingDecision: routingDecision,
        routingMetrics: routingDecision.metrics
      };
    } catch (error) {
      throw new Error(`边缘路由失败: ${error.message}`);
    }
  }

  private async makeRoutingDecision(
    clientInfo: any,
    policy: string,
    nodes: any[],
    metrics: any
  ): Promise<any> {
    const availableNodes = nodes.filter(node => node.status === 'active');
    
    if (availableNodes.length === 0) {
      throw new Error('没有可用的边缘节点');
    }

    let selectedNode;
    const routingMetrics = {
      totalNodes: nodes.length,
      availableNodes: availableNodes.length,
      policy: policy,
      timestamp: Date.now()
    };

    switch (policy) {
      case 'latency':
        selectedNode = this.selectByLatency(clientInfo, availableNodes, metrics);
        break;
      case 'load':
        selectedNode = this.selectByLoad(availableNodes);
        break;
      case 'geographic':
        selectedNode = this.selectByGeography(clientInfo, availableNodes);
        break;
      case 'cost':
        selectedNode = this.selectByCost(availableNodes);
        break;
      default:
        selectedNode = availableNodes[0];
    }

    return {
      selectedNode,
      policy,
      metrics: routingMetrics,
      timestamp: Date.now()
    };
  }

  private selectByLatency(clientInfo: any, nodes: any[], metrics: any): any {
    return nodes.reduce((best, current) => {
      const currentLatency = metrics[current.nodeId]?.latency || Infinity;
      const bestLatency = metrics[best.nodeId]?.latency || Infinity;
      return currentLatency < bestLatency ? current : best;
    });
  }

  private selectByLoad(nodes: any[]): any {
    return nodes.reduce((best, current) => {
      const currentLoad = current.load || 0;
      const bestLoad = best.load || 0;
      return currentLoad < bestLoad ? current : best;
    });
  }

  private selectByGeography(clientInfo: any, nodes: any[]): any {
    if (!clientInfo.location) return nodes[0];
    
    return nodes.reduce((best, current) => {
      const currentDistance = this.calculateDistance(
        clientInfo.location,
        current.location
      );
      const bestDistance = this.calculateDistance(
        clientInfo.location,
        best.location
      );
      return currentDistance < bestDistance ? current : best;
    });
  }

  private selectByCost(nodes: any[]): any {
    return nodes.reduce((best, current) => {
      const currentCost = current.cost || Infinity;
      const bestCost = best.cost || Infinity;
      return currentCost < bestCost ? current : best;
    });
  }

  private calculateDistance(loc1: any, loc2: any): number {
    if (!loc1 || !loc2) return Infinity;
    
    const R = 6371; // 地球半径（公里）
    const dLat = this.deg2rad(loc2.latitude - loc1.latitude);
    const dLon = this.deg2rad(loc2.longitude - loc1.longitude);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(loc1.latitude)) * Math.cos(this.deg2rad(loc2.latitude)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI/180);
  }
}

/**
 * 边缘负载均衡节点
 * 提供边缘节点间的负载均衡功能
 */
export class EdgeLoadBalancingNode extends BaseNode {
  constructor() {
    super('EdgeLoadBalancingNode', '边缘负载均衡', '边缘计算');
    
    this.inputs = [
      { name: 'edgeNodes', type: 'array', label: '边缘节点列表' },
      { name: 'balancingAlgorithm', type: 'string', label: '负载均衡算法' },
      { name: 'requestInfo', type: 'object', label: '请求信息' },
      { name: 'healthChecks', type: 'object', label: '健康检查结果' }
    ];
    
    this.outputs = [
      { name: 'targetNode', type: 'object', label: '目标节点' },
      { name: 'loadDistribution', type: 'object', label: '负载分布' },
      { name: 'balancingMetrics', type: 'object', label: '负载均衡指标' }
    ];
  }

  async execute(inputs: Record<string, any>): Promise<Record<string, any>> {
    const { 
      edgeNodes = [], 
      balancingAlgorithm = 'round_robin', 
      requestInfo = {},
      healthChecks = {}
    } = inputs;

    try {
      const result = await this.performLoadBalancing(
        edgeNodes,
        balancingAlgorithm,
        requestInfo,
        healthChecks
      );

      return {
        targetNode: result.targetNode,
        loadDistribution: result.loadDistribution,
        balancingMetrics: result.metrics
      };
    } catch (error) {
      throw new Error(`边缘负载均衡失败: ${error.message}`);
    }
  }

  private async performLoadBalancing(
    nodes: any[],
    algorithm: string,
    requestInfo: any,
    healthChecks: any
  ): Promise<any> {
    const healthyNodes = nodes.filter(node => 
      healthChecks[node.nodeId]?.status === 'healthy' || !healthChecks[node.nodeId]
    );

    if (healthyNodes.length === 0) {
      throw new Error('没有健康的边缘节点可用');
    }

    let targetNode;
    const loadDistribution = this.calculateLoadDistribution(healthyNodes);

    switch (algorithm) {
      case 'round_robin':
        targetNode = this.roundRobinSelection(healthyNodes);
        break;
      case 'least_connections':
        targetNode = this.leastConnectionsSelection(healthyNodes);
        break;
      case 'weighted_round_robin':
        targetNode = this.weightedRoundRobinSelection(healthyNodes);
        break;
      case 'least_response_time':
        targetNode = this.leastResponseTimeSelection(healthyNodes, healthChecks);
        break;
      case 'resource_based':
        targetNode = this.resourceBasedSelection(healthyNodes);
        break;
      default:
        targetNode = healthyNodes[0];
    }

    const metrics = {
      algorithm,
      totalNodes: nodes.length,
      healthyNodes: healthyNodes.length,
      selectedNodeId: targetNode.nodeId,
      timestamp: Date.now()
    };

    return {
      targetNode,
      loadDistribution,
      metrics
    };
  }

  private calculateLoadDistribution(nodes: any[]): any {
    const distribution = {};
    const totalLoad = nodes.reduce((sum, node) => sum + (node.currentLoad || 0), 0);
    
    nodes.forEach(node => {
      distribution[node.nodeId] = {
        currentLoad: node.currentLoad || 0,
        percentage: totalLoad > 0 ? ((node.currentLoad || 0) / totalLoad) * 100 : 0,
        capacity: node.capacity || 100
      };
    });

    return distribution;
  }

  private roundRobinSelection(nodes: any[]): any {
    // 简单轮询选择
    const index = Math.floor(Math.random() * nodes.length);
    return nodes[index];
  }

  private leastConnectionsSelection(nodes: any[]): any {
    return nodes.reduce((best, current) => {
      const currentConnections = current.activeConnections || 0;
      const bestConnections = best.activeConnections || 0;
      return currentConnections < bestConnections ? current : best;
    });
  }

  private weightedRoundRobinSelection(nodes: any[]): any {
    const totalWeight = nodes.reduce((sum, node) => sum + (node.weight || 1), 0);
    let random = Math.random() * totalWeight;
    
    for (const node of nodes) {
      random -= (node.weight || 1);
      if (random <= 0) {
        return node;
      }
    }
    
    return nodes[0];
  }

  private leastResponseTimeSelection(nodes: any[], healthChecks: any): any {
    return nodes.reduce((best, current) => {
      const currentResponseTime = healthChecks[current.nodeId]?.responseTime || Infinity;
      const bestResponseTime = healthChecks[best.nodeId]?.responseTime || Infinity;
      return currentResponseTime < bestResponseTime ? current : best;
    });
  }

  private resourceBasedSelection(nodes: any[]): any {
    return nodes.reduce((best, current) => {
      const currentUtilization = (current.cpuUsage || 0) + (current.memoryUsage || 0);
      const bestUtilization = (best.cpuUsage || 0) + (best.memoryUsage || 0);
      return currentUtilization < bestUtilization ? current : best;
    });
  }
}

/**
 * 边缘缓存节点
 * 提供边缘缓存管理功能
 */
export class EdgeCachingNode extends BaseNode {
  constructor() {
    super('EdgeCachingNode', '边缘缓存', '边缘计算');

    this.inputs = [
      { name: 'cacheKey', type: 'string', label: '缓存键' },
      { name: 'cacheValue', type: 'any', label: '缓存值' },
      { name: 'operation', type: 'string', label: '操作类型' },
      { name: 'ttl', type: 'number', label: '生存时间(秒)' },
      { name: 'cachePolicy', type: 'object', label: '缓存策略' }
    ];

    this.outputs = [
      { name: 'result', type: 'any', label: '操作结果' },
      { name: 'cacheHit', type: 'boolean', label: '缓存命中' },
      { name: 'cacheStats', type: 'object', label: '缓存统计' }
    ];
  }

  private cache = new Map<string, { value: any; expiry: number; accessCount: number; lastAccess: number }>();
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0
  };

  async execute(inputs: Record<string, any>): Promise<Record<string, any>> {
    const {
      cacheKey,
      cacheValue,
      operation = 'get',
      ttl = 3600,
      cachePolicy = {}
    } = inputs;

    try {
      let result;
      let cacheHit = false;

      switch (operation) {
        case 'get':
          result = this.getCacheValue(cacheKey);
          cacheHit = result !== null;
          break;
        case 'set':
          result = this.setCacheValue(cacheKey, cacheValue, ttl);
          break;
        case 'delete':
          result = this.deleteCacheValue(cacheKey);
          break;
        case 'clear':
          result = this.clearCache();
          break;
        case 'exists':
          result = this.cacheExists(cacheKey);
          break;
        default:
          throw new Error(`不支持的缓存操作: ${operation}`);
      }

      // 执行缓存策略
      this.applyCachePolicy(cachePolicy);

      return {
        result,
        cacheHit,
        cacheStats: this.getCacheStats()
      };
    } catch (error) {
      throw new Error(`边缘缓存操作失败: ${error.message}`);
    }
  }

  private getCacheValue(key: string): any {
    const item = this.cache.get(key);

    if (!item) {
      this.stats.misses++;
      return null;
    }

    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      this.stats.misses++;
      this.stats.evictions++;
      return null;
    }

    // 更新访问统计
    item.accessCount++;
    item.lastAccess = Date.now();
    this.stats.hits++;

    return item.value;
  }

  private setCacheValue(key: string, value: any, ttl: number): boolean {
    const expiry = Date.now() + (ttl * 1000);

    this.cache.set(key, {
      value,
      expiry,
      accessCount: 0,
      lastAccess: Date.now()
    });

    this.stats.sets++;
    return true;
  }

  private deleteCacheValue(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.stats.deletes++;
    }
    return deleted;
  }

  private clearCache(): number {
    const size = this.cache.size;
    this.cache.clear();
    this.stats.deletes += size;
    return size;
  }

  private cacheExists(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;

    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      this.stats.evictions++;
      return false;
    }

    return true;
  }

  private applyCachePolicy(policy: any): void {
    const maxSize = policy.maxSize || 1000;
    const evictionPolicy = policy.evictionPolicy || 'lru';

    if (this.cache.size > maxSize) {
      this.evictItems(this.cache.size - maxSize, evictionPolicy);
    }
  }

  private evictItems(count: number, policy: string): void {
    const items = Array.from(this.cache.entries());

    let toEvict: string[] = [];

    switch (policy) {
      case 'lru': // Least Recently Used
        toEvict = items
          .sort((a, b) => a[1].lastAccess - b[1].lastAccess)
          .slice(0, count)
          .map(item => item[0]);
        break;
      case 'lfu': // Least Frequently Used
        toEvict = items
          .sort((a, b) => a[1].accessCount - b[1].accessCount)
          .slice(0, count)
          .map(item => item[0]);
        break;
      case 'fifo': // First In First Out
        toEvict = Array.from(this.cache.keys()).slice(0, count);
        break;
      default:
        toEvict = Array.from(this.cache.keys()).slice(0, count);
    }

    toEvict.forEach(key => {
      this.cache.delete(key);
      this.stats.evictions++;
    });
  }

  private getCacheStats(): any {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;

    return {
      ...this.stats,
      size: this.cache.size,
      hitRate: hitRate.toFixed(2) + '%',
      totalRequests
    };
  }
}

/**
 * 边缘压缩节点
 * 提供数据压缩和解压缩功能
 */
export class EdgeCompressionNode extends BaseNode {
  constructor() {
    super('EdgeCompressionNode', '边缘压缩', '边缘计算');

    this.inputs = [
      { name: 'data', type: 'any', label: '输入数据' },
      { name: 'operation', type: 'string', label: '操作类型' },
      { name: 'compressionType', type: 'string', label: '压缩类型' },
      { name: 'compressionLevel', type: 'number', label: '压缩级别' }
    ];

    this.outputs = [
      { name: 'result', type: 'any', label: '处理结果' },
      { name: 'compressionRatio', type: 'number', label: '压缩比' },
      { name: 'processingTime', type: 'number', label: '处理时间' }
    ];
  }

  async execute(inputs: Record<string, any>): Promise<Record<string, any>> {
    const {
      data,
      operation = 'compress',
      compressionType = 'gzip',
      compressionLevel = 6
    } = inputs;

    const startTime = Date.now();

    try {
      let result;
      let originalSize = this.getDataSize(data);
      let compressedSize = originalSize;

      switch (operation) {
        case 'compress':
          result = await this.compressData(data, compressionType, compressionLevel);
          compressedSize = this.getDataSize(result);
          break;
        case 'decompress':
          result = await this.decompressData(data, compressionType);
          compressedSize = originalSize;
          originalSize = this.getDataSize(result);
          break;
        default:
          throw new Error(`不支持的压缩操作: ${operation}`);
      }

      const processingTime = Date.now() - startTime;
      const compressionRatio = originalSize > 0 ? compressedSize / originalSize : 1;

      return {
        result,
        compressionRatio: parseFloat(compressionRatio.toFixed(4)),
        processingTime
      };
    } catch (error) {
      throw new Error(`边缘压缩操作失败: ${error.message}`);
    }
  }

  private async compressData(data: any, type: string, level: number): Promise<any> {
    const dataString = typeof data === 'string' ? data : JSON.stringify(data);

    switch (type) {
      case 'gzip':
        return this.gzipCompress(dataString, level);
      case 'deflate':
        return this.deflateCompress(dataString, level);
      case 'brotli':
        return this.brotliCompress(dataString, level);
      case 'lz4':
        return this.lz4Compress(dataString);
      default:
        throw new Error(`不支持的压缩类型: ${type}`);
    }
  }

  private async decompressData(data: any, type: string): Promise<any> {
    switch (type) {
      case 'gzip':
        return this.gzipDecompress(data);
      case 'deflate':
        return this.deflateDecompress(data);
      case 'brotli':
        return this.brotliDecompress(data);
      case 'lz4':
        return this.lz4Decompress(data);
      default:
        throw new Error(`不支持的解压缩类型: ${type}`);
    }
  }

  private gzipCompress(data: string, level: number): string {
    // 模拟gzip压缩
    const compressed = this.simpleCompress(data, level);
    return `gzip:${compressed}`;
  }

  private gzipDecompress(data: string): string {
    if (!data.startsWith('gzip:')) {
      throw new Error('无效的gzip数据格式');
    }
    return this.simpleDecompress(data.substring(5));
  }

  private deflateCompress(data: string, level: number): string {
    const compressed = this.simpleCompress(data, level);
    return `deflate:${compressed}`;
  }

  private deflateDecompress(data: string): string {
    if (!data.startsWith('deflate:')) {
      throw new Error('无效的deflate数据格式');
    }
    return this.simpleDecompress(data.substring(8));
  }

  private brotliCompress(data: string, level: number): string {
    const compressed = this.simpleCompress(data, level);
    return `brotli:${compressed}`;
  }

  private brotliDecompress(data: string): string {
    if (!data.startsWith('brotli:')) {
      throw new Error('无效的brotli数据格式');
    }
    return this.simpleDecompress(data.substring(7));
  }

  private lz4Compress(data: string): string {
    const compressed = this.simpleCompress(data, 1);
    return `lz4:${compressed}`;
  }

  private lz4Decompress(data: string): string {
    if (!data.startsWith('lz4:')) {
      throw new Error('无效的lz4数据格式');
    }
    return this.simpleDecompress(data.substring(4));
  }

  private simpleCompress(data: string, level: number): string {
    // 简单的压缩模拟：基于压缩级别移除重复字符
    const compressionFactor = Math.max(0.1, 1 - (level * 0.1));
    const targetLength = Math.floor(data.length * compressionFactor);

    if (targetLength >= data.length) {
      return Buffer.from(data).toString('base64');
    }

    // 简单的重复字符压缩
    let compressed = '';
    let i = 0;
    while (i < data.length && compressed.length < targetLength) {
      const char = data[i];
      let count = 1;

      while (i + count < data.length && data[i + count] === char && count < 255) {
        count++;
      }

      if (count > 3) {
        compressed += `${char}${count}`;
      } else {
        compressed += char.repeat(count);
      }

      i += count;
    }

    return Buffer.from(compressed).toString('base64');
  }

  private simpleDecompress(compressedData: string): string {
    try {
      const data = Buffer.from(compressedData, 'base64').toString();

      // 简单的重复字符解压缩
      let decompressed = '';
      let i = 0;

      while (i < data.length) {
        const char = data[i];

        if (i + 1 < data.length && /\d/.test(data[i + 1])) {
          const count = parseInt(data[i + 1]);
          decompressed += char.repeat(count);
          i += 2;
        } else {
          decompressed += char;
          i++;
        }
      }

      return decompressed;
    } catch (error) {
      throw new Error('解压缩失败：数据格式错误');
    }
  }

  private getDataSize(data: any): number {
    if (typeof data === 'string') {
      return Buffer.byteLength(data, 'utf8');
    } else if (Buffer.isBuffer(data)) {
      return data.length;
    } else {
      return Buffer.byteLength(JSON.stringify(data), 'utf8');
    }
  }
}
